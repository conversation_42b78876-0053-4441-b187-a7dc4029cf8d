"""
生产环境设置
"""

from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='').split(',')

# 生产环境安全设置
SECURE_SSL_REDIRECT = config('SECURE_SSL_REDIRECT', default=False, cast=bool)
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# 生产环境会话设置
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# 生产环境邮件设置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

# 生产环境日志设置
LOGGING['handlers']['file']['filename'] = '/var/log/django/production.log'
LOGGING['handlers']['console']['level'] = 'WARNING'
LOGGING['loggers']['django']['level'] = 'WARNING'

# 生产环境缓存设置（Redis推荐）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': config('REDIS_URL', default='redis://127.0.0.1:6379/1'),
    }
}

# 生产环境静态文件设置
STATIC_ROOT = '/var/www/static/'
MEDIA_ROOT = '/var/www/media/'

# 生产环境数据库连接池设置
DATABASES['default']['CONN_MAX_AGE'] = 60

# 生产环境CORS设置
CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = config('CORS_ALLOWED_ORIGINS', default='').split(',')

# 生产环境错误报告
ADMINS = [
    ('Admin', config('ADMIN_EMAIL', default='<EMAIL>')),
]

MANAGERS = ADMINS

# 生产环境性能优化
USE_ETAGS = True
PREPEND_WWW = False
