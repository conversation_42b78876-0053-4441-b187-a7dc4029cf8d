version: '3.8'

services:
  mariadb:
    image: mariadb:10.11
    container_name: align_mariadb
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: align_project
      MYSQL_USER: align_user
      MYSQL_PASSWORD: align_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - ./docker/mariadb/data:/var/lib/mysql
      - ./docker/mariadb/init:/docker-entrypoint-initdb.d
      - ./docker/mariadb/conf:/etc/mysql/conf.d
    networks:
      - align_network

  web:
    build: .
    container_name: align_backend
    restart: unless-stopped
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn align_project.wsgi:application --bind 0.0.0.0:8000"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=align_project.settings.production
    depends_on:
      - mariadb
    networks:
      - align_network

  nginx:
    image: nginx:alpine
    container_name: align_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - static_volume:/var/www/static
      - media_volume:/var/www/media
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
    networks:
      - align_network

volumes:
  static_volume:
  media_volume:

networks:
  align_network:
    driver: bridge
