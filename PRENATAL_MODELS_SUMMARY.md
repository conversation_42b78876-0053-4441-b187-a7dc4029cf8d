# 产检相关数据库模型创建总结

## 概述

已成功创建了完整的产检管理系统，包含三个核心数据库模型，支持标准产检项目管理、自定义产检项目创建以及完整的产检记录跟踪功能。

## 创建的文件和功能

### 1. 数据库模型 (`apps/prenatal/models.py` + `apps/prenatal/image_models.py`)

#### PrenatalItem (标准产检项目表)
- **主键**: UUID
- **字段**: name(项目名称), content(详细内容), start_week(开始孕周), end_week(结束孕周)
- **验证**: 孕周范围1-42周，开始周不能大于结束周
- **方法**: `is_applicable_for_week()` 检查孕周适用性
- **索引**: (start_week, end_week), name

#### CustomPrenatalItem (自定义产检项目表)
- **继承**: PrenatalItem
- **额外字段**: created_by(创建者外键)
- **关系**: 与UserAccount的外键关系
- **索引**: created_by

#### PrenatalCheckup (产检记录表)
- **主键**: UUID
- **用户关系**: user(外键关联UserAccount)
- **时间字段**: date(产检日期), time(产检时间), travel_time(出行时间), reminder_time(提醒时间)
- **地点**: location(产检地点)
- **多对多关系**: 
  - prenatal_items(标准产检项目)
  - custom_prenatal_items(自定义产检项目)
- **笔记字段**: preparation_notes(准备笔记), checkup_notes(产检笔记)
- **图片存储**: checkup_images(JSON字段存储URL列表)
- **状态管理**: status(scheduled/completed/cancelled/rescheduled)
- **验证**: 时间验证、提醒时间验证
- **方法**: `add_image()`, `remove_image()`, `get_all_items()`
- **属性**: `is_upcoming`, `is_past_due`
- **索引**: (user, date, time), (date, time)

#### PrenatalImage (产检图片表)
- **主键**: UUID
- **用户关系**: user(外键关联UserAccount)
- **图片文件**: image(ImageField，支持jpg/png/gif等格式)
- **图片信息**: image_type(图片类型), title(标题), description(描述)
- **元数据**: file_size(文件大小), width/height(图片尺寸)
- **关联**: checkup(可选关联产检记录)
- **属性**: `file_size_human`, `image_url`, `thumbnail_url`
- **索引**: (user, created_at), (user, image_type), checkup, created_at

#### ImageUploadSession (图片上传会话表)
- **主键**: UUID
- **用户关系**: user(外键关联UserAccount)
- **会话信息**: session_name(会话名称), total_files(总文件数)
- **进度跟踪**: uploaded_files(已上传), failed_files(失败数)
- **状态**: status(pending/uploading/completed/failed)
- **关联**: checkup(可选关联产检记录)
- **属性**: `progress_percentage`, `is_completed`
- **方法**: `mark_completed()`, `increment_uploaded()`, `increment_failed()`

### 2. Django Admin管理 (`apps/prenatal/admin.py`)

- **PrenatalItemAdmin**: 标准产检项目管理，支持孕周范围显示
- **CustomPrenatalItemAdmin**: 自定义产检项目管理
- **PrenatalCheckupAdmin**: 产检记录管理，支持筛选、搜索、项目数量显示

### 3. API序列化器 (`apps/prenatal/serializers.py`)

- **PrenatalItemSerializer**: 标准产检项目序列化
- **CustomPrenatalItemSerializer**: 自定义产检项目序列化，自动设置创建者
- **PrenatalCheckupSerializer**: 产检记录详细序列化，包含验证逻辑
- **PrenatalCheckupListSerializer**: 产检记录列表简化序列化

### 4. API视图 (`apps/prenatal/views.py`)

- **PrenatalItemViewSet**: 标准产检项目视图集（只读）
  - `by_week()` 动作：根据孕周获取适用项目
- **CustomPrenatalItemViewSet**: 自定义产检项目视图集（完整CRUD）
- **PrenatalCheckupViewSet**: 产检记录视图集（完整CRUD）
  - `upcoming()` 动作：获取即将到来的产检
  - `past_due()` 动作：获取已过期的产检
  - `add_image()` 动作：添加产检图片
  - `remove_image()` 动作：移除产检图片

### 5. 图片服务 (`apps/prenatal/image_*`)

- **image_models.py**: 图片相关数据模型
- **image_serializers.py**: 图片API序列化器
- **image_views.py**: 图片管理视图集
- **test_image_service.py**: 图片服务测试
- **IMAGE_SERVICE_README.md**: 图片服务详细文档

### 6. URL配置 (`apps/prenatal/urls.py`)

- `/api/prenatal/items/` - 标准产检项目
- `/api/prenatal/custom-items/` - 自定义产检项目
- `/api/prenatal/checkups/` - 产检记录
- `/api/prenatal/images/` - 产检图片管理
- `/api/prenatal/upload-sessions/` - 上传会话管理
- `/api/prenatal/images/quick-upload/` - 快速上传
- `/api/prenatal/images/types/` - 图片类型选项

### 7. 数据库迁移

- **0001_initial.py**: 创建所有表结构和索引
- **0002_add_initial_prenatal_items.py**: 添加15种预置标准产检项目
- **0003_remove_status_field.py**: 移除产检记录的status字段
- **0004_add_image_models.py**: 添加图片服务相关表

### 7. 测试文件 (`apps/prenatal/tests.py`)

包含完整的单元测试：
- 模型创建和验证测试
- 孕周范围验证测试
- 产检记录管理测试
- 图片添加/删除功能测试

### 8. 文档 (`apps/prenatal/README.md`)

详细的模块使用说明和API文档。

## 预置的标准产检项目 (15项)

1. **建档检查** (6-12周) - 基本体格检查、血常规等
2. **NT检查** (11-13周) - 胎儿颈项透明层厚度测量
3. **早期唐氏筛查** (9-13周) - 血清学检查
4. **中期唐氏筛查** (15-20周) - 染色体异常筛查
5. **四维彩超** (20-24周) - 大排畸检查
6. **糖耐量试验** (24-28周) - 妊娠期糖尿病筛查
7. **胎心监护** (32-42周) - 胎儿心率监测
8. **生物物理评分** (36-42周) - 胎儿状况评估
9. **骨盆测量** (36-38周) - 分娩方式评估
10. **血常规** (1-42周) - 贫血和感染监测
11. **尿常规** (1-42周) - 肾功能监测
12. **体重血压测量** (1-42周) - 孕期监测
13. **宫高腹围测量** (20-42周) - 胎儿发育评估
14. **羊水穿刺** (16-20周) - 染色体诊断
15. **无创DNA检测** (12-22周) - 染色体异常筛查

## 数据库表结构

### prenatal_items (标准产检项目表)
```sql
- id (UUID, 主键)
- name (VARCHAR(100), 项目名称)
- content (TEXT, 项目内容)
- start_week (INT, 开始孕周)
- end_week (INT, 结束孕周)
- created_at (DATETIME, 创建时间)
- updated_at (DATETIME, 更新时间)
```

### custom_prenatal_items (自定义产检项目表)
```sql
- prenatalitem_ptr_id (UUID, 主键，外键到prenatal_items)
- created_by_id (BIGINT, 外键到user_accounts)
```

### prenatal_checkups (产检记录表)
```sql
- id (UUID, 主键)
- user_id (BIGINT, 外键到user_accounts)
- date (DATE, 产检日期)
- time (TIME, 产检时间)
- location (VARCHAR(200), 产检地点)
- travel_time (TIME, 出行时间)
- reminder_time (TIME, 提醒时间)
- preparation_notes (TEXT, 准备笔记)
- checkup_notes (TEXT, 产检笔记)
- checkup_images (JSON, 图片URL列表)
- status (VARCHAR(20), 状态)
- created_at (DATETIME, 创建时间)
- updated_at (DATETIME, 更新时间)
```

## API端点示例

### 获取标准产检项目
```
GET /api/prenatal/items/
GET /api/prenatal/items/?week=20  # 根据孕周筛选
GET /api/prenatal/items/by_week/?week=20  # 获取20周适用项目
```

### 管理自定义产检项目
```
GET /api/prenatal/custom-items/  # 获取当前用户的自定义项目
POST /api/prenatal/custom-items/  # 创建自定义项目
PUT /api/prenatal/custom-items/{id}/  # 更新自定义项目
DELETE /api/prenatal/custom-items/{id}/  # 删除自定义项目
```

### 管理产检记录
```
GET /api/prenatal/checkups/  # 获取产检记录列表
POST /api/prenatal/checkups/  # 创建产检记录
GET /api/prenatal/checkups/{id}/  # 获取产检记录详情
PUT /api/prenatal/checkups/{id}/  # 更新产检记录
DELETE /api/prenatal/checkups/{id}/  # 删除产检记录
GET /api/prenatal/checkups/upcoming/  # 获取即将到来的产检
GET /api/prenatal/checkups/past_due/  # 获取已过期的产检
POST /api/prenatal/checkups/{id}/add_image/  # 添加图片
POST /api/prenatal/checkups/{id}/remove_image/  # 移除图片
```

## 配置完成

1. ✅ 模型已添加到 `forum_project/settings/base.py` 的 `INSTALLED_APPS`
2. ✅ 数据库迁移已执行，表结构已创建
3. ✅ 初始数据已添加（15种标准产检项目）
4. ✅ Django Admin已配置
5. ✅ API路由已添加到 `apps/api/urls.py`
6. ✅ 系统检查通过，无错误

## 使用建议

1. **权限控制**: 所有API都需要用户认证
2. **数据验证**: 模型和序列化器都包含完整的数据验证
3. **性能优化**: 已添加数据库索引，查询使用了prefetch_related
4. **扩展性**: 模型设计支持未来功能扩展
5. **测试**: 建议运行测试确保功能正常（需要配置测试数据库权限）
