from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

# 导入各应用的视图
from apps.users.views import UserViewSet, EmailVerificationCodeViewSet, LoginLogViewSet
from apps.authentication.views import (
    CustomTokenObtainPairView,
    EmailCodeLoginView,
    SendVerificationCodeView,
    LogoutView,
    verify_token
)

# 创建路由器
router = DefaultRouter()

# 注册用户相关路由
router.register(r'users', UserViewSet, basename='user')
router.register(r'verification-codes', EmailVerificationCodeViewSet, basename='verification-code')
router.register(r'login-logs', LoginLogViewSet, basename='login-log')

urlpatterns = [
    # 认证相关路由
    path('auth/login/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/login/email-code/', EmailCodeLoginView.as_view(), name='email_code_login'),
    path('auth/send-code/', SendVerificationCodeView.as_view(), name='send_verification_code'),
    path('auth/logout/', LogoutView.as_view(), name='logout'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/verify/', verify_token, name='verify_token'),

    # 产检管理相关路由
    path('prenatal/', include('apps.prenatal.urls')),

    # 包含路由器生成的URL
    path('', include(router.urls)),
]
