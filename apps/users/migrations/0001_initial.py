# Generated by Django 5.2.4 on 2025-08-10 18:44

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='邮箱地址')),
                ('is_active', models.BooleanField(default=True, verbose_name='激活状态')),
                ('is_staff', models.BooleanField(default=False, verbose_name='管理员状态')),
                ('is_superuser', models.BooleanField(default=False, verbose_name='超级用户状态')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='注册时间')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='最后登录IP')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='useraccount_set', related_query_name='useraccount', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='useraccount_set', related_query_name='useraccount', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户账户',
                'verbose_name_plural': '用户账户',
                'db_table': 'user_accounts',
                'ordering': ['-date_joined'],
            },
        ),
        migrations.CreateModel(
            name='EmailVerificationCode',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, verbose_name='邮箱地址')),
                ('code', models.CharField(max_length=6, verbose_name='验证码')),
                ('code_type', models.CharField(choices=[('register', '注册验证'), ('login', '登录验证'), ('reset_password', '重置密码'), ('change_email', '更换邮箱')], max_length=20, verbose_name='验证码类型')),
                ('is_used', models.BooleanField(default=False, verbose_name='已使用')),
                ('used_at', models.DateTimeField(blank=True, null=True, verbose_name='使用时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='请求IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
            ],
            options={
                'verbose_name': '邮箱验证码',
                'verbose_name_plural': '邮箱验证码',
                'db_table': 'email_verification_codes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['email', 'code_type', 'is_used'], name='email_verif_email_edf78a_idx'), models.Index(fields=['created_at'], name='email_verif_created_028ca8_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=150, unique=True, verbose_name='用户名')),
                ('nickname', models.CharField(blank=True, max_length=50, verbose_name='昵称')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
                ('bio', models.TextField(blank=True, max_length=500, verbose_name='个人简介')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='手机号')),
                ('expected_delivery_date', models.DateField(blank=True, help_text='预计分娩日期', null=True, verbose_name='预产期')),
                ('is_email_verified', models.BooleanField(default=False, verbose_name='邮箱已验证')),
                ('is_phone_verified', models.BooleanField(default=False, verbose_name='手机已验证')),
                ('receive_email_notifications', models.BooleanField(default=True, verbose_name='接收邮件通知')),
                ('receive_sms_notifications', models.BooleanField(default=False, verbose_name='接收短信通知')),
                ('post_count', models.PositiveIntegerField(default=0, verbose_name='发帖数')),
                ('comment_count', models.PositiveIntegerField(default=0, verbose_name='评论数')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='获赞数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='用户账户')),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
                'db_table': 'user_profiles',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, verbose_name='登录邮箱')),
                ('login_type', models.CharField(choices=[('password', '密码登录'), ('email_code', '邮箱验证码登录'), ('phone_code', '手机验证码登录'), ('social', '第三方登录')], max_length=20, verbose_name='登录方式')),
                ('status', models.CharField(choices=[('success', '成功'), ('failed', '失败'), ('blocked', '被阻止')], max_length=20, verbose_name='登录状态')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='登录地点')),
                ('failure_reason', models.CharField(blank=True, max_length=200, verbose_name='失败原因')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='登录时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'db_table': 'login_logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='login_logs_user_id_783da5_idx'), models.Index(fields=['email', 'status'], name='login_logs_email_4ab659_idx'), models.Index(fields=['ip_address'], name='login_logs_ip_addr_669c36_idx')],
            },
        ),
    ]
