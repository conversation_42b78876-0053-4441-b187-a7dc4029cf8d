# Generated by Django 5.2.4 on 2025-08-10 19:13

from django.db import migrations, models
import django.utils.timezone


def split_datetime_to_date_time(apps, schema_editor):
    """将现有的datetime字段数据分离到date和time字段"""
    PrenatalCheckup = apps.get_model('prenatal', 'PrenatalCheckup')

    for checkup in PrenatalCheckup.objects.all():
        if checkup.datetime:
            # 将datetime分离为date和time
            checkup.date = checkup.datetime.date()
            checkup.time = checkup.datetime.time()
            checkup.save(update_fields=['date', 'time'])


def merge_date_time_to_datetime(apps, schema_editor):
    """将date和time字段合并回datetime字段（回滚操作）"""
    PrenatalCheckup = apps.get_model('prenatal', 'PrenatalCheckup')

    for checkup in PrenatalCheckup.objects.all():
        if checkup.date and checkup.time:
            # 将date和time合并为datetime
            import datetime
            checkup_datetime = datetime.datetime.combine(checkup.date, checkup.time)
            checkup.datetime = django.utils.timezone.make_aware(checkup_datetime)
            checkup.save(update_fields=['datetime'])


class Migration(migrations.Migration):

    dependencies = [
        ('prenatal', '0004_add_image_models'),
    ]

    operations = [
        # 第一步：添加新的date和time字段（允许为空）
        migrations.AddField(
            model_name='prenatalcheckup',
            name='date',
            field=models.DateField(
                verbose_name='产检日期',
                help_text='预约的产检日期',
                null=True,
                blank=True
            ),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='time',
            field=models.TimeField(
                verbose_name='产检时间',
                help_text='预约的产检时间（小时和分钟）',
                null=True,
                blank=True
            ),
        ),

        # 第二步：数据迁移 - 将datetime数据分离到date和time字段
        migrations.RunPython(
            split_datetime_to_date_time,
            reverse_code=merge_date_time_to_datetime,
        ),

        # 第三步：将新字段设为非空
        migrations.AlterField(
            model_name='prenatalcheckup',
            name='date',
            field=models.DateField(
                verbose_name='产检日期',
                help_text='预约的产检日期'
            ),
        ),
        migrations.AlterField(
            model_name='prenatalcheckup',
            name='time',
            field=models.TimeField(
                verbose_name='产检时间',
                help_text='预约的产检时间（小时和分钟）'
            ),
        ),

        # 第四步：删除旧的索引
        migrations.RemoveIndex(
            model_name='prenatalcheckup',
            name='prenatal_ch_user_id_4e3dec_idx',
        ),
        migrations.RemoveIndex(
            model_name='prenatalcheckup',
            name='prenatal_ch_datetim_2f8819_idx',
        ),

        # 第五步：删除旧的datetime字段
        migrations.RemoveField(
            model_name='prenatalcheckup',
            name='datetime',
        ),

        # 第六步：更新模型选项和添加新索引
        migrations.AlterModelOptions(
            name='prenatalcheckup',
            options={
                'ordering': ['-date', '-time'],
                'verbose_name': '产检记录',
                'verbose_name_plural': '产检记录'
            },
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['user', 'date', 'time'], name='prenatal_ch_user_id_4e3dec_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['date', 'time'], name='prenatal_ch_date_2f8819_idx'),
        ),
    ]
