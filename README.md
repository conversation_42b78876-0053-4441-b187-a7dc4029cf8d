# Align Backend - 通用项目后端服务

一个基于Django框架开发的现代化后端系统，提供完整的用户管理、项目管理和API服务。

## 🚀 项目特性

- **现代化架构**: 基于Django 4.x构建，采用模块化设计
- **多种登录方式**: 支持邮箱登录和验证码登录
- **强大的后台管理**: 使用Django Admin进行内容管理
- **RESTful API**: 提供完整的API接口供前端或第三方调用
- **容器化部署**: 支持Docker部署，包含MariaDB数据库
- **高性能数据库**: 使用MariaDB作为主数据库
- **现代包管理**: 使用uv进行依赖管理

## 📋 技术栈

- **后端框架**: Django 4.x
- **数据库**: MariaDB 10.x
- **包管理**: uv
- **容器化**: Docker & Docker Compose
- **API框架**: Django REST Framework
- **认证**: Django内置认证 + 自定义验证码系统

## 🏗️ 项目结构

```
align-backend/
├── align_project/          # Django主项目
│   ├── settings/           # 分环境配置
│   │   ├── base.py        # 基础配置
│   │   ├── development.py # 开发环境
│   │   └── production.py  # 生产环境
│   ├── urls.py            # 主URL配置
│   ├── wsgi.py            # WSGI配置
│   └── asgi.py            # ASGI配置
├── apps/                   # 应用模块
│   ├── users/             # 用户管理模块
│   ├── authentication/    # 认证系统模块
│   ├── api/              # API接口模块
│   └── prenatal/         # 产检管理模块
├── static/                # 静态文件
├── media/                 # 用户上传文件
├── docker/               # Docker配置文件
├── docs/                 # 项目文档
├── tests/                # 测试文件
├── Dockerfile            # Docker镜像构建文件
├── docker-compose.yml    # Docker编排文件
├── pyproject.toml        # 项目配置和依赖
└── README.md            # 项目说明文档
```

## 🛠️ 环境要求

- Python 3.12+
- uv (Python包管理器)
- Docker & Docker Compose
- MariaDB 10.x (通过Docker提供)

## 📦 安装和配置

### 1. 克隆项目

```bash
git clone <repository-url>
cd align-backend
```

### 2. 安装uv (如果尚未安装)

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 3. 安装项目依赖

```bash
uv sync
```

### 4. 启动MariaDB数据库

```bash
# 启动MariaDB容器（包含数据持久化）
docker-compose up -d mariadb
```

### 5. 数据库迁移

```bash
# 创建数据库迁移文件
uv run python manage.py makemigrations

# 执行数据库迁移
uv run python manage.py migrate

# 创建超级用户
uv run python manage.py createsuperuser
```

### 6. 启动开发服务器

```bash
uv run python manage.py runserver
```

## 🗄️ 数据库配置

### MariaDB Docker配置

项目使用Docker来运行MariaDB数据库，配置如下：

- **端口**: 3306
- **数据库名**: align_project
- **用户名**: align_user
- **密码**: align_password
- **数据持久化**: `./docker/mariadb/data`

### 连接配置

数据库连接配置位于 `align_project/settings/base.py`：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'align_project',
        'USER': 'align_user',
        'PASSWORD': 'align_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

## 🔐 用户认证系统

### 支持的登录方式

1. **邮箱密码登录**: 传统的邮箱+密码登录方式
2. **验证码登录**: 通过邮箱发送验证码进行登录

### 认证API端点

- `POST /api/auth/login/` - 邮箱密码登录
- `POST /api/auth/send-code/` - 发送验证码
- `POST /api/auth/verify-code/` - 验证码登录
- `POST /api/auth/logout/` - 用户登出
- `POST /api/auth/refresh/` - 刷新Token

## 🎛️ Django Admin管理

访问 `http://localhost:8000/admin/` 进入管理后台。

### 管理功能

- **用户管理**: 用户账户、权限、分组管理
- **产检管理**: 产检记录、项目管理
- **系统设置**: 站点配置、邮件设置

## 📡 API接口

### 基础URL

- 开发环境: `http://localhost:8000/api/`
- 生产环境: `https://your-domain.com/api/`

### 主要API端点

#### 用户相关
- `GET /api/users/` - 获取用户列表
- `GET /api/users/{id}/` - 获取用户详情
- `PUT /api/users/{id}/` - 更新用户信息

#### 产检管理相关
- `GET /api/prenatal/items/` - 获取产检项目列表
- `GET /api/prenatal/checkups/` - 获取产检记录列表
- `POST /api/prenatal/checkups/` - 创建产检记录
- `GET /api/prenatal/checkups/{id}/` - 获取产检记录详情

### API认证

使用JWT Token进行API认证：

```bash
# 获取Token
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 使用Token访问API
curl -X GET http://localhost:8000/api/users/ \
  -H "Authorization: Bearer <your-token>"
```

## 🐳 Docker部署

### 开发环境

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境

```bash
# 构建生产镜像
docker build -t align-backend:latest .

# 使用生产配置启动
docker-compose -f docker-compose.prod.yml up -d
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
uv run python manage.py test

# 运行特定应用的测试
uv run python manage.py test apps.users

# 运行测试并生成覆盖率报告
uv run coverage run --source='.' manage.py test
uv run coverage report
```

## 📚 开发指南

### 添加新的依赖

```bash
# 添加生产依赖
uv add django-rest-framework

# 添加开发依赖
uv add --dev pytest-django
```

### 创建新的应用

```bash
# 在apps目录下创建新应用
uv run python manage.py startapp new_app apps/new_app
```

### 数据库操作

```bash
# 创建迁移文件
uv run python manage.py makemigrations

# 查看迁移状态
uv run python manage.py showmigrations

# 执行迁移
uv run python manage.py migrate

# 回滚迁移
uv run python manage.py migrate app_name migration_name
```

## 🔧 配置说明

### 环境变量

创建 `.env` 文件配置环境变量：

```env
# Django设置
DEBUG=True
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库设置
DB_NAME=align_project
DB_USER=align_user
DB_PASSWORD=align_password
DB_HOST=localhost
DB_PORT=3306

# 邮件设置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True
```

### 日志配置

日志文件位置：
- 开发环境: `logs/debug.log`
- 生产环境: `logs/production.log`

## 🚀 部署指南

### 1. 准备生产环境

```bash
# 设置环境变量
export DJANGO_SETTINGS_MODULE=align_project.settings.production

# 收集静态文件
uv run python manage.py collectstatic --noinput

# 执行数据库迁移
uv run python manage.py migrate
```

### 2. 使用Docker部署

```bash
# 构建镜像
docker build -t align-backend:latest .

# 启动服务
docker-compose -f docker-compose.prod.yml up -d
```

### 3. 使用传统方式部署

```bash
# 安装生产服务器
uv add gunicorn

# 启动Gunicorn
uv run gunicorn align_project.wsgi:application --bind 0.0.0.0:8000
```

## 📖 API文档

详细的API文档可以通过以下方式访问：

- **Swagger UI**: `http://localhost:8000/api/docs/`
- **ReDoc**: `http://localhost:8000/api/redoc/`
- **OpenAPI Schema**: `http://localhost:8000/api/schema/`

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您遇到任何问题或有任何建议，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 🔄 更新日志

### v0.1.0 (2024-01-XX)
- 初始版本发布
- 基础用户认证系统
- 论坛核心功能
- Django Admin管理界面
- RESTful API接口
- Docker部署支持